import dotenv from 'dotenv';
import { Config } from '../types';

dotenv.config();

// Detect if we're running on Hardhat
const isHardhat = process.env.CHAIN_ID === '31337' || process.env.RPC_URL?.includes('localhost:8545');

export const config: Config = {
  rpcUrl: process.env.RPC_URL || (isHardhat ? 'http://localhost:8545' : 'https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY'),
  flashbotsRpcUrl: process.env.FLASHBOTS_RPC_URL || (isHardhat ? 'http://localhost:8545' : 'https://relay.flashbots.net'),
  chainId: parseInt(process.env.CHAIN_ID || (isHardhat ? '31337' : '11155111')), // Default to Hardhat if detected, otherwise Sepolia
  privateKey: process.env.PRIVATE_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  flashbotsSignerKey: process.env.FLASHBOTS_SIGNER_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  minProfitWei: process.env.MIN_PROFIT_WEI || '1000000000000000000',
  maxGasPriceGwei: parseInt(process.env.MAX_GAS_PRICE_GWEI || '100'),
  maxPriorityFeeGwei: parseInt(process.env.MAX_PRIORITY_FEE_GWEI || '10'),
  slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.005'),
  mempoolWebsocketUrl: process.env.MEMPOOL_WEBSOCKET_URL || (isHardhat ? 'ws://localhost:8545' : 'wss://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY'),
  enableFlashbotsMempool: process.env.ENABLE_FLASHBOTS_MEMPOOL === 'true' && !isHardhat, // Disable Flashbots on Hardhat
  enableEthersMempool: process.env.ENABLE_ETHERS_MEMPOOL !== 'false',
  enableSandwichAttacks: process.env.ENABLE_SANDWICH_ATTACKS === 'true',
  enableFrontRunning: process.env.ENABLE_FRONT_RUNNING === 'true',
  enableArbitrage: process.env.ENABLE_ARBITRAGE === 'true',
  enableFlashloanAttacks: process.env.ENABLE_FLASHLOAN_ATTACKS === 'true',
  enableUniswapV3FlashSwaps: process.env.ENABLE_UNISWAP_V3_FLASH_SWAPS === 'true',
  enableMultiBlockAttacks: process.env.ENABLE_MULTI_BLOCK_ATTACKS === 'true',
  maxBlocksAhead: parseInt(process.env.MAX_BLOCKS_AHEAD || '3'),
  maxPositionSizeEth: parseInt(process.env.MAX_POSITION_SIZE_ETH || '10'),
  emergencyStop: process.env.EMERGENCY_STOP === 'false',
  dryRun: process.env.DRY_RUN !== 'true',
  simulationMode: process.env.SIMULATION_MODE === 'true',
  logLevel: process.env.LOG_LEVEL || 'info',
  logToFile: process.env.LOG_TO_FILE === 'true',
  // Flashloan contract addresses
  hybridFlashloanContract: process.env.HYBRID_FLASHLOAN_CONTRACT || '',
  balancerFlashloanContract: process.env.BALANCER_FLASHLOAN_CONTRACT || '',
  aaveFlashloanContract: process.env.AAVE_FLASHLOAN_CONTRACT || '',
  // Flashloan DEX configuration
  flashloanDexPairs: (process.env.FLASHLOAN_DEX_PAIRS || 'UNISWAP_V2,UNISWAP_V3').split(','),
  flashloanBuyDex: process.env.FLASHLOAN_BUY_DEX || 'UNISWAP_V2',
  flashloanSellDex: process.env.FLASHLOAN_SELL_DEX || 'UNISWAP_V3',
  enableCrossDexArbitrage: process.env.ENABLE_CROSS_DEX_ARBITRAGE === 'true',
  minArbitrageSpread: parseFloat(process.env.MIN_ARBITRAGE_SPREAD || '0.5'),
  // Flashloan token configuration
  flashloanTokens: (process.env.FLASHLOAN_TOKENS || 'USDC,WETH,USDT,DAI').split(','),
  flashloanPrimaryToken: process.env.FLASHLOAN_PRIMARY_TOKEN || 'USDC',
  flashloanTargetTokens: (process.env.FLASHLOAN_TARGET_TOKENS || 'WETH,USDT,DAI').split(','),
  enableAllTokenPairs: process.env.ENABLE_ALL_TOKEN_PAIRS === 'true',
  minTokenLiquidityUsd: parseFloat(process.env.MIN_TOKEN_LIQUIDITY_USD || '100000'),
  // Flashloan amount configuration
  flashloanBaseAmountWeth: parseFloat(process.env.FLASHLOAN_BASE_AMOUNT_WETH || '5'),
  flashloanBaseAmountUsdc: parseFloat(process.env.FLASHLOAN_BASE_AMOUNT_USDC || '20000'),
  flashloanMaxMultiplier: parseInt(process.env.FLASHLOAN_MAX_MULTIPLIER || '10'),
  // Flashbots configuration
  enableFlashbots: process.env.ENABLE_FLASHBOTS === 'true' && !isHardhat, // Disable Flashbots on Hardhat
  flashbotsRelayUrl: process.env.FLASHBOTS_RELAY_URL || (isHardhat ? 'http://localhost:8545' : 'https://relay.flashbots.net'),
  flashbotsAuthKey: process.env.FLASHBOTS_AUTH_KEY || '',
  // Uniswap V3 Flash Swap configuration
  uniswapV3TradingPairs: (process.env.UNISWAP_V3_TRADING_PAIRS || 'WETH/USDC,WETH/USDT,WBTC/WETH,DAI/USDC').split(','),
  uniswapV3FeeTiers: (process.env.UNISWAP_V3_FEE_TIERS || '500,3000,10000').split(',').map(Number),
  // MEV-Share configuration
  enableMevShare: process.env.ENABLE_MEV_SHARE === 'true',
  mevShareStreamUrl: process.env.MEV_SHARE_STREAM_URL || 'https://mev-share.flashbots.net',
  enableBackrunStrategy: process.env.ENABLE_BACKRUN_STRATEGY === 'true',
  minBackrunProfitEth: parseFloat(process.env.MIN_BACKRUN_PROFIT_ETH || '0.01'),
  maxGasCostEth: parseFloat(process.env.MAX_GAS_COST_ETH || '0.02'),
  // Scanning intervals (in milliseconds)
  arbitrageScanIntervalMs: parseInt(process.env.ARBITRAGE_SCAN_INTERVAL_MS || '0'), // 0 = auto
  flashloanScanIntervalMs: parseInt(process.env.FLASHLOAN_SCAN_INTERVAL_MS || '0'), // 0 = auto
  // Advanced gas estimation
  blocknativeApiKey: process.env.BLOCKNATIVE_API_KEY || '',
  enableBlocknativeGas: process.env.ENABLE_BLOCKNATIVE_GAS === 'true',
  enable0xApiGas: process.env.ENABLE_0X_API_GAS === 'true',
  enableEthGasStation: process.env.ENABLE_ETH_GAS_STATION === 'true',
  fallbackGasPriceGwei: parseFloat(process.env.FALLBACK_GAS_PRICE || '20')
};

// Network-specific addresses
export const MAINNET_ADDRESSES = {
  // Mainnet WETH
  WETH: '******************************************',
  // Mainnet tokens
  USDC: '******************************************', // USDC
  USDT: '******************************************', // USDT
  DAI: '******************************************', // DAI
  WBTC: '******************************************', // WBTC
  // Uniswap V2 on Mainnet
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  // Aave V3 on Mainnet
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',
  AAVE_POOL: '******************************************',
  // Additional DEXs for mainnet
  SUSHISWAP_ROUTER: '******************************************',
  PANCAKESWAP_ROUTER: '******************************************',
  BALANCER_VAULT: '******************************************',
  CURVE_REGISTRY: '******************************************',
  // Curve 3pool (USDC/DAI/USDT) - Low slippage stablecoin swaps
  CURVE_3POOL: '******************************************'
};

export const SEPOLIA_ADDRESSES = {
  // Sepolia WETH
  WETH: '******************************************',
  // Test tokens on Sepolia
  USDC: '******************************************',
  USDT: '******************************************',
  DAI: '******************************************',
  WBTC: '******************************************', // Test WBTC on Sepolia
  // Uniswap V2 on Sepolia
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  // Aave V3 on Sepolia
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',
  AAVE_POOL: '******************************************',
  // Additional DEXs for Sepolia (limited availability)
  SUSHISWAP_ROUTER: '', // Not available on Sepolia
  PANCAKESWAP_ROUTER: '', // Not available on Sepolia
  BALANCER_VAULT: '******************************************', // Same address on all networks
  CURVE_REGISTRY: '', // Not available on Sepolia
  CURVE_3POOL: '' // Not available on Sepolia
};

// Select addresses based on chain ID
export const ADDRESSES = config.chainId === 1 ? MAINNET_ADDRESSES : SEPOLIA_ADDRESSES;

// Comprehensive ERC-20 token definitions (Top tokens by market cap and trading volume)
const MAINNET_TOKENS = [
  // Stablecoins (Primary flashloan tokens)
  { address: MAINNET_ADDRESSES.USDC, symbol: 'USDC' as TokenSymbol, decimals: 6, name: 'USD Coin', category: 'stablecoin' as TokenCategory, priority: 1 },
  { address: MAINNET_ADDRESSES.USDT, symbol: 'USDT' as TokenSymbol, decimals: 6, name: 'Tether USD', category: 'stablecoin' as TokenCategory, priority: 2 },
  { address: MAINNET_ADDRESSES.DAI, symbol: 'DAI' as TokenSymbol, decimals: 18, name: 'Dai Stablecoin', category: 'stablecoin' as TokenCategory, priority: 3 },

  // Major cryptocurrencies
  { address: MAINNET_ADDRESSES.WETH, symbol: 'WETH' as TokenSymbol, decimals: 18, name: 'Wrapped Ether', category: 'major' as TokenCategory, priority: 1 },
  { address: MAINNET_ADDRESSES.WBTC, symbol: 'WBTC' as TokenSymbol, decimals: 8, name: 'Wrapped Bitcoin', category: 'major' as TokenCategory, priority: 2 },

  // DeFi tokens (High liquidity)
  { address: '******************************************', symbol: 'UNI' as TokenSymbol, decimals: 18, name: 'Uniswap', category: 'defi' as TokenCategory, priority: 1 },
  { address: '******************************************', symbol: 'LINK' as TokenSymbol, decimals: 18, name: 'Chainlink', category: 'defi' as TokenCategory, priority: 2 },
  { address: '******************************************', symbol: 'AAVE' as TokenSymbol, decimals: 18, name: 'Aave', category: 'defi' as TokenCategory, priority: 3 },
  { address: '******************************************', symbol: 'CRV' as TokenSymbol, decimals: 18, name: 'Curve DAO Token', category: 'defi' as TokenCategory, priority: 4 },
  { address: '******************************************', symbol: 'COMP' as TokenSymbol, decimals: 18, name: 'Compound', category: 'defi' as TokenCategory, priority: 5 },

  // Layer 2 tokens
  { address: '0x4200000000000000000000000000000000000042', symbol: 'OP' as TokenSymbol, decimals: 18, name: 'Optimism', category: 'layer2' as TokenCategory, priority: 1 },
  { address: '0x7D1AfA7B718fb893dB30A3aBc0Cfc608AaCfeBB0', symbol: 'MATIC' as TokenSymbol, decimals: 18, name: 'Polygon', category: 'layer2' as TokenCategory, priority: 2 },

  // Meme tokens (High volatility, good for arbitrage)
  { address: '0x95aD61b0a150d79219dCF64E1E6Cc01f0B64C4cE', symbol: 'SHIB' as TokenSymbol, decimals: 18, name: 'Shiba Inu', category: 'meme' as TokenCategory, priority: 1 },
  { address: '0x4d224452801ACEd8B2F0aebE155379bb5D594381', symbol: 'APE' as TokenSymbol, decimals: 18, name: 'ApeCoin', category: 'meme' as TokenCategory, priority: 2 },

  // Exchange tokens
  { address: '******************************************', symbol: 'BNB' as TokenSymbol, decimals: 18, name: 'Binance Coin', category: 'exchange' as TokenCategory, priority: 1 },
  { address: '******************************************', symbol: 'FTT' as TokenSymbol, decimals: 18, name: 'FTX Token', category: 'exchange' as TokenCategory, priority: 2 }
];

const SEPOLIA_TOKENS = [
  // Core tokens for flashloan arbitrage on testnet
  { address: SEPOLIA_ADDRESSES.WETH, symbol: 'WETH' as TokenSymbol, decimals: 18, name: 'Wrapped Ether', category: 'major' as TokenCategory, priority: 1 },
  { address: SEPOLIA_ADDRESSES.USDC, symbol: 'USDC' as TokenSymbol, decimals: 6, name: 'USD Coin', category: 'stablecoin' as TokenCategory, priority: 1 },
  { address: SEPOLIA_ADDRESSES.USDT, symbol: 'USDT' as TokenSymbol, decimals: 6, name: 'Tether USD', category: 'stablecoin' as TokenCategory, priority: 2 },
  { address: SEPOLIA_ADDRESSES.DAI, symbol: 'DAI' as TokenSymbol, decimals: 18, name: 'Dai Stablecoin', category: 'stablecoin' as TokenCategory, priority: 3 }
];

// Select tokens based on chain ID
export const COMMON_TOKENS = config.chainId === 1 ? MAINNET_TOKENS : SEPOLIA_TOKENS;

// DEX Configuration
export type DexName = 'UNISWAP_V2' | 'UNISWAP_V3' | 'SUSHISWAP' | 'PANCAKESWAP' | 'BALANCER' | 'CURVE';

export interface DexConfig {
  name: DexName;
  router: string;
  protocol: 'uniswap-v2' | 'uniswap-v3' | 'balancer' | 'curve';
  available: boolean;
  fees?: number[]; // Available fee tiers for V3-style DEXs
  poolAddress?: string; // For Curve pools
}

// Get DEX configuration based on current network
export function getDexConfig(): Record<DexName, DexConfig> {
  const isMainnet = config.chainId === 1;

  return {
    UNISWAP_V2: {
      name: 'UNISWAP_V2',
      router: ADDRESSES.UNISWAP_V2_ROUTER,
      protocol: 'uniswap-v2',
      available: true
    },
    UNISWAP_V3: {
      name: 'UNISWAP_V3',
      router: ADDRESSES.UNISWAP_V3_ROUTER,
      protocol: 'uniswap-v3',
      available: true,
      fees: [500, 3000, 10000] // 0.05%, 0.3%, 1%
    },
    SUSHISWAP: {
      name: 'SUSHISWAP',
      router: ADDRESSES.SUSHISWAP_ROUTER,
      protocol: 'uniswap-v2',
      available: isMainnet && ADDRESSES.SUSHISWAP_ROUTER !== ''
    },
    PANCAKESWAP: {
      name: 'PANCAKESWAP',
      router: ADDRESSES.PANCAKESWAP_ROUTER,
      protocol: 'uniswap-v2',
      available: isMainnet && ADDRESSES.PANCAKESWAP_ROUTER !== ''
    },
    BALANCER: {
      name: 'BALANCER',
      router: ADDRESSES.BALANCER_VAULT,
      protocol: 'balancer',
      available: ADDRESSES.BALANCER_VAULT !== ''
    },
    CURVE: {
      name: 'CURVE',
      router: ADDRESSES.CURVE_REGISTRY,
      protocol: 'curve',
      available: isMainnet && ADDRESSES.CURVE_3POOL !== '',
      poolAddress: ADDRESSES.CURVE_3POOL,
      fees: [4] // 0.04% for Curve 3pool
    }
  };
}

// Get available DEX pairs for flashloan arbitrage
export function getAvailableDexPairs(): DexName[] {
  const dexConfig = getDexConfig();
  return Object.values(dexConfig)
    .filter(dex => dex.available)
    .map(dex => dex.name);
}

// Token Configuration
export type TokenSymbol = 'WETH' | 'USDC' | 'USDT' | 'DAI' | 'WBTC' | 'UNI' | 'LINK' | 'AAVE' | 'CRV' | 'COMP' | 'OP' | 'MATIC' | 'SHIB' | 'APE' | 'BNB' | 'FTT';
export type TokenCategory = 'stablecoin' | 'major' | 'defi' | 'layer2' | 'meme' | 'exchange';

export interface TokenConfig {
  address: string;
  symbol: TokenSymbol;
  decimals: number;
  name: string;
  category: TokenCategory;
  priority: number;
}

// Get all available tokens for current network
export function getAllTokens(): TokenConfig[] {
  return config.chainId === 1 ? MAINNET_TOKENS : SEPOLIA_TOKENS;
}

// Get configured tokens for flashloan arbitrage
export function getConfiguredTokens(): TokenConfig[] {
  const allTokens = getAllTokens();
  const configuredSymbols = config.flashloanTokens || ['USDC', 'WETH'];

  return allTokens.filter(token =>
    configuredSymbols.includes(token.symbol)
  );
}

// Get primary flashloan token
export function getPrimaryFlashloanToken(): TokenConfig | null {
  const allTokens = getAllTokens();
  const primarySymbol = config.flashloanPrimaryToken || 'USDC';

  return allTokens.find(token => token.symbol === primarySymbol) || null;
}

// Get target tokens for arbitrage
export function getTargetTokens(): TokenConfig[] {
  const allTokens = getAllTokens();
  const targetSymbols = config.flashloanTargetTokens || ['WETH', 'USDT', 'DAI'];

  return allTokens.filter(token =>
    targetSymbols.includes(token.symbol)
  );
}

// Get tokens by category
export function getTokensByCategory(category: TokenCategory): TokenConfig[] {
  const allTokens = getAllTokens();
  return allTokens.filter(token => token.category === category);
}

// Get high-priority tokens (best for arbitrage)
export function getHighPriorityTokens(): TokenConfig[] {
  const allTokens = getAllTokens();
  return allTokens
    .filter(token => token.priority <= 2) // Top 2 in each category
    .sort((a, b) => a.priority - b.priority);
}

// Validate token configuration
export function validateTokenConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const allTokens = getAllTokens();
  const allSymbols = allTokens.map(t => t.symbol);

  // Check if primary token exists
  const primaryToken = config.flashloanPrimaryToken;
  if (primaryToken && !allSymbols.includes(primaryToken as TokenSymbol)) {
    errors.push(`Primary flashloan token '${primaryToken}' not available on current network`);
  }

  // Check if configured tokens exist
  const configuredTokens = config.flashloanTokens || [];
  for (const symbol of configuredTokens) {
    if (!allSymbols.includes(symbol as TokenSymbol)) {
      errors.push(`Configured token '${symbol}' not available on current network`);
    }
  }

  // Check if target tokens exist
  const targetTokens = config.flashloanTargetTokens || [];
  for (const symbol of targetTokens) {
    if (!allSymbols.includes(symbol as TokenSymbol)) {
      errors.push(`Target token '${symbol}' not available on current network`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function validateConfig(): void {
  // Import logger here to avoid circular dependency
  const { logger } = require('../utils/logger');

  if (config.privateKey === '0x0000000000000000000000000000000000000000000000000000000000000000') {
    logger.warn('⚠️  Using default private key - please set PRIVATE_KEY in .env');
  }

  if (config.rpcUrl.includes('YOUR_ALCHEMY_KEY')) {
    logger.warn('⚠️  Using default RPC URL - please set RPC_URL or ALCHEMY_API_KEY in .env');
  }

  if (config.dryRun) {
    logger.info('🧪 Running in DRY RUN mode - no real transactions will be sent');
  }

  if (config.simulationMode) {
    logger.info('🎭 Running in SIMULATION mode - opportunities will be detected and analyzed but not executed');
  }

  const networkName = config.chainId === 11155111 ? 'Sepolia Testnet' :
                     config.chainId === 1 ? 'Ethereum Mainnet' :
                     `Chain ${config.chainId}`;

  // Validate token configuration
  const tokenValidation = validateTokenConfig();
  if (!tokenValidation.valid) {
    console.warn('⚠️  Token configuration issues:');
    tokenValidation.errors.forEach(error => console.warn(`   • ${error}`));
  }

  // Get configured tokens info
  const configuredTokens = getConfiguredTokens();
  const primaryToken = getPrimaryFlashloanToken();
  const targetTokens = getTargetTokens();

  logger.info(`🔧 Configuration loaded:
    - Network: ${networkName}
    - Chain ID: ${config.chainId}
    - Min Profit: ${config.minProfitWei} wei
    - Max Gas Price: ${config.maxGasPriceGwei} gwei
    - Slippage Tolerance: ${config.slippageTolerance * 100}%
    - Strategies: ${[
      config.enableSandwichAttacks && 'Sandwich',
      config.enableFrontRunning && 'Front-running',
      config.enableArbitrage && 'Arbitrage',
      config.enableFlashloanAttacks && 'Flashloan'
    ].filter(Boolean).join(', ')}
    - Flashloan Tokens: ${configuredTokens.map(t => t.symbol).join(', ')}
    - Primary Token: ${primaryToken?.symbol || 'None'}
    - Target Tokens: ${targetTokens.map(t => t.symbol).join(', ')}
    - DEX Pairs: ${config.flashloanDexPairs.join(', ')}
  `);

  if (config.chainId === 11155111) {
    console.log('🧪 Running on Sepolia testnet - perfect for safe testing!');
  }

  if (config.enableFlashloanAttacks && configuredTokens.length === 0) {
    console.warn('⚠️  Flashloan attacks enabled but no tokens configured!');
  }
}
